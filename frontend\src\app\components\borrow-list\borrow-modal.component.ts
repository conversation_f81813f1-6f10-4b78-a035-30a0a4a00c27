import { Compo<PERSON>, OnInit, Inject } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from "@angular/forms";
import {
  MatDialogModule,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatNativeDateModule } from "@angular/material/core";
import { MatSnackBarModule, MatSnackBar } from "@angular/material/snack-bar";
import { MatAutocompleteModule } from "@angular/material/autocomplete";
import { Observable, startWith, map } from "rxjs";
import {
  BorrowService,
  CreateBorrowRequest,
} from "../../services/borrow.service";
import { BookService } from "../../services/book.service";
import { MemberService } from "../../services/member.service";
import { Book, BookOnShelf } from "../../models/book.model";
import { Member } from "../../models/member.model";

@Component({
  selector: "app-borrow-modal",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatAutocompleteModule,
  ],
  template: `
    <div class="borrow-modal">
      <h2 mat-dialog-title>
        <mat-icon>library_add</mat-icon>
        Cho mượn sách
      </h2>

      <form [formGroup]="borrowForm" (ngSubmit)="onSubmit()">
        <mat-dialog-content>
          <div class="form-content">
            <!-- Book Selection -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Chọn sách</mat-label>
              <input
                type="text"
                matInput
                formControlName="bookSearch"
                [matAutocomplete]="bookAuto"
                placeholder="Nhập tên sách hoặc tác giả"
              />
              <mat-autocomplete
                #bookAuto="matAutocomplete"
                [displayWith]="displayBookFn"
                (optionSelected)="onBookSelected($event)"
              >
                <mat-option
                  *ngFor="let book of filteredBooks | async"
                  [value]="book"
                >
                  <div class="book-option">
                    <div class="book-title">{{ book.title }}</div>
                    <div class="book-author">{{ book.author }}</div>
                    <div class="book-location">📍 {{ book.location }}</div>
                    <div
                      class="book-availability"
                      [class.available]="book.stockQuantity > 0"
                      [class.unavailable]="book.stockQuantity <= 0"
                    >
                      Có sẵn: {{ book.stockQuantity }}
                    </div>
                  </div>
                </mat-option>
              </mat-autocomplete>
              <mat-icon matSuffix>book</mat-icon>
              <mat-error *ngIf="borrowForm.get('bookId')?.hasError('required')">
                Vui lòng chọn sách
              </mat-error>
            </mat-form-field>

            <!-- Selected Book Display -->
            <div *ngIf="selectedBook" class="selected-book">
              <mat-icon>check_circle</mat-icon>
              <div class="book-info">
                <strong>{{ selectedBook.title }}</strong>
                <div class="book-details">
                  <span>Tác giả: {{ selectedBook.author }}</span>
                  <span>📍 Vị trí: {{ selectedBook.location }}</span>
                  <span>Có sẵn: {{ selectedBook.stockQuantity }}</span>
                </div>
              </div>
            </div>

            <!-- Member Selection -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Chọn thành viên</mat-label>
              <input
                type="text"
                matInput
                formControlName="memberSearch"
                [matAutocomplete]="memberAuto"
                placeholder="Nhập tên hoặc email thành viên"
              />
              <mat-autocomplete
                #memberAuto="matAutocomplete"
                [displayWith]="displayMemberFn"
                (optionSelected)="onMemberSelected($event)"
              >
                <mat-option
                  *ngFor="let member of filteredMembers | async"
                  [value]="member"
                >
                  <div class="member-option">
                    <div class="member-name">{{ member.fullName }}</div>
                    <div class="member-email">{{ member.email }}</div>
                    <div
                      class="member-status"
                      [class.active]="member.status === 1"
                      [class.inactive]="member.status !== 1"
                    >
                      {{ getStatusName(member.status) }}
                    </div>
                  </div>
                </mat-option>
              </mat-autocomplete>
              <mat-icon matSuffix>person</mat-icon>
              <mat-error
                *ngIf="borrowForm.get('memberId')?.hasError('required')"
              >
                Vui lòng chọn thành viên
              </mat-error>
            </mat-form-field>

            <!-- Selected Member Display -->
            <div *ngIf="selectedMember" class="selected-member">
              <mat-icon>check_circle</mat-icon>
              <div class="member-info">
                <strong>{{ selectedMember.fullName }}</strong>
                <div class="member-details">
                  <span>Email: {{ selectedMember.email }}</span>
                  <span
                    >Trạng thái:
                    {{ getStatusName(selectedMember.status) }}</span
                  >
                </div>
              </div>
            </div>

            <!-- Due Date -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Hạn trả</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                formControlName="dueDate"
                [min]="minDate"
                [max]="maxDate"
              />
              <mat-hint>Hạn trả tối đa {{ maxBorrowDays }} ngày</mat-hint>
              <mat-datepicker-toggle
                matSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error
                *ngIf="borrowForm.get('dueDate')?.hasError('required')"
              >
                Vui lòng chọn hạn trả
              </mat-error>
            </mat-form-field>

            <!-- Notes -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Ghi chú (tùy chọn)</mat-label>
              <textarea
                matInput
                formControlName="notes"
                rows="3"
                maxlength="500"
                placeholder="Nhập ghi chú về việc mượn sách này"
              ></textarea>
              <mat-hint
                >{{ borrowForm.get("notes")?.value?.length || 0 }}/500</mat-hint
              >
            </mat-form-field>
          </div>
        </mat-dialog-content>

        <mat-dialog-actions align="end">
          <button mat-button type="button" (click)="onCancel()">Hủy</button>
          <button
            mat-raised-button
            color="primary"
            type="submit"
            [disabled]="borrowForm.invalid || loading"
          >
            <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
            <mat-icon *ngIf="!loading">save</mat-icon>
            {{ loading ? "Đang xử lý..." : "Cho mượn" }}
          </button>
        </mat-dialog-actions>
      </form>
    </div>
  `,
  styles: [
    `
      .borrow-modal {
        min-width: 500px;
        max-width: 600px;
      }

      h2[mat-dialog-title] {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
      }

      .form-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
        min-height: 400px;
      }

      .full-width {
        width: 100%;
      }

      .book-option,
      .member-option {
        padding: 8px 0;
      }

      .book-title,
      .member-name {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .book-author,
      .member-email {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .book-availability,
      .member-status {
        font-size: 11px;
        padding: 2px 6px;
        border-radius: 4px;
      }

      .book-availability.available {
        background-color: #e8f5e8;
        color: #2e7d2e;
      }

      .book-availability.unavailable {
        background-color: #ffebee;
        color: #c62828;
      }

      .member-status.active {
        background-color: #e8f5e8;
        color: #2e7d2e;
      }

      .member-status.inactive {
        background-color: #ffebee;
        color: #c62828;
      }

      .selected-book,
      .selected-member {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
        background-color: #f5f5f5;
        border-radius: 8px;
        border-left: 4px solid #4caf50;
      }

      .selected-book mat-icon,
      .selected-member mat-icon {
        color: #4caf50;
        margin-top: 2px;
      }

      .book-info,
      .member-info {
        flex: 1;
      }

      .book-details,
      .member-details {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
      }

      .book-details span,
      .member-details span {
        margin-right: 16px;
      }

      mat-dialog-actions {
        padding: 20px 0 0 0;
        margin: 0;
      }

      @media (max-width: 600px) {
        .borrow-modal {
          min-width: 90vw;
        }
      }
    `,
  ],
})
export class BorrowModalComponent implements OnInit {
  borrowForm: FormGroup;
  loading = false;

  books: BookOnShelf[] = [];
  members: Member[] = [];
  selectedBook: BookOnShelf | null = null;
  selectedMember: Member | null = null;

  filteredBooks: Observable<BookOnShelf[]>;
  filteredMembers: Observable<Member[]>;

  minDate = new Date();
  maxDate = new Date();
  maxBorrowDays = 30;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<BorrowModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private borrowService: BorrowService,
    private bookService: BookService,
    private memberService: MemberService,
    private snackBar: MatSnackBar
  ) {
    this.borrowForm = this.fb.group({
      bookId: ["", Validators.required],
      bookSearch: [""],
      memberId: ["", Validators.required],
      memberSearch: [""],
      dueDate: ["", Validators.required],
      notes: [""],
    });

    // Set default due date (14 days from now)
    const defaultDueDate = new Date();
    defaultDueDate.setDate(defaultDueDate.getDate() + 14);
    this.borrowForm.patchValue({ dueDate: defaultDueDate });

    // Set max date (30 days from now)
    this.maxDate.setDate(this.maxDate.getDate() + this.maxBorrowDays);

    // Setup autocomplete
    this.filteredBooks = this.borrowForm.get("bookSearch")!.valueChanges.pipe(
      startWith(""),
      map((value) => this._filterBooks(value))
    );

    this.filteredMembers = this.borrowForm
      .get("memberSearch")!
      .valueChanges.pipe(
        startWith(""),
        map((value) => this._filterMembers(value))
      );
  }

  ngOnInit(): void {
    this.loadBooks();
    this.loadMembers();
  }

  loadBooks(): void {
    console.log("Đang tải danh sách sách...");
    this.bookService.getBooksOnShelf().subscribe({
      next: (books: BookOnShelf[]) => {
        console.log("Đã tải sách:", books);
        this.books = books;
      },
      error: (err) => {
        console.error("Lỗi khi tải sách trên kệ:", err);
        this.snackBar.open("Không thể tải sách có sẵn trên kệ", "Đóng", {
          duration: 3000,
        });
      },
    });
  }

  loadMembers(): void {
    this.memberService.getMembers().subscribe({
      next: (members) => {
        this.members = members.filter((member) => member.status === 1); // Only active members
      },
      error: (error) => {
        console.error("Error loading members:", error);
        this.snackBar.open("Lỗi khi tải danh sách thành viên", "Đóng", {
          duration: 3000,
        });
      },
    });
  }

  private _filterBooks(value: string | BookOnShelf): BookOnShelf[] {
    if (typeof value === "object") return this.books;
    const filterValue = value.toLowerCase();
    return this.books.filter(
      (b) =>
        b.title.toLowerCase().includes(filterValue) ||
        b.author.toLowerCase().includes(filterValue)
    );
  }

  private _filterMembers(value: string | Member): Member[] {
    if (typeof value === "object") return this.members;

    const filterValue = value.toLowerCase();
    return this.members.filter(
      (member) =>
        member.fullName.toLowerCase().includes(filterValue) ||
        member.email.toLowerCase().includes(filterValue)
    );
  }

  displayBookFn(book: BookOnShelf): string {
    return book ? `${book.title} - ${book.author} (${book.bookshelfName})` : "";
  }

  displayMemberFn(member: Member): string {
    return member ? `${member.fullName} (${member.email})` : "";
  }

  onBookSelected(event: any): void {
    this.selectedBook = event.option.value;
    if (this.selectedBook) {
      this.borrowForm.patchValue({
        bookId: this.selectedBook.id,
      });
      
      // Tạo location string từ bookshelfName và locationCode
      if (this.selectedBook.bookshelfName && this.selectedBook.locationCode) {
        this.selectedBook.location = `${this.selectedBook.bookshelfName} - ${this.selectedBook.locationCode}`;
      } else if (this.selectedBook.bookshelfName) {
        this.selectedBook.location = this.selectedBook.bookshelfName;
      }
    }
  }

  onMemberSelected(event: any): void {
    this.selectedMember = event.option.value;
    if (this.selectedMember) {
      this.borrowForm.patchValue({ memberId: this.selectedMember.id });
    }
  }

  getStatusName(status: number): string {
    switch (status) {
      case 1:
        return "Hoạt động";
      case 2:
        return "Tạm ngưng";
      case 3:
        return "Hết hạn";
      case 4:
        return "Bị cấm";
      default:
        return "Không xác định";
    }
  }

  onSubmit(): void {
    if (this.borrowForm.valid && !this.loading) {
      this.loading = true;

      const formValue = this.borrowForm.value;
      const request: CreateBorrowRequest = {
        bookId: formValue.bookId,
        memberId: formValue.memberId,
        dueDate: formValue.dueDate.toISOString(),
        notes: formValue.notes || undefined,
      };

      this.borrowService.borrowBook(request).subscribe({
        next: (result) => {
          this.snackBar.open("Cho mượn sách thành công", "Đóng", {
            duration: 3000,
          });
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error("Error creating borrow record:", error);
          let errorMessage = "Lỗi khi cho mượn sách";
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.error && typeof error.error === "string") {
            errorMessage = error.error;
          }
          this.snackBar.open(errorMessage, "Đóng", { duration: 5000 });
          this.loading = false;
        },
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
