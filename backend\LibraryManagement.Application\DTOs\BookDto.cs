namespace LibraryManagement.Application.DTOs;

public class BookDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public string? Publisher { get; set; }
    public DateTime? PublishedDate { get; set; }
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    
    // Số lượng tổng (StockQuantity + OnShelfQuantity)
    public int Quantity { get; set; }
    
    // Số lượng trong kho
    public int StockQuantity { get; set; }
    
    // Số lượng trên kệ
    public int OnShelfQuantity { get; set; }
    
    // Số lượng đang được mượn
    public int BorrowedQuantity { get; set; }
    
    // Số lượng có sẵn để mượn (StockQuantity + OnShelfQuantity)
    public int AvailableQuantity => StockQuantity + OnShelfQuantity;

    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public decimal? Price { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Thông tin về kệ sách
    public int? BookshelfId { get; set; }
    public string? BookshelfName { get; set; }
    public string? LocationCode { get; set; }
    public string? Location { get; set; }
}

public class CreateBookDto
{
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public string? Publisher { get; set; }
    public DateTime? PublishedDate { get; set; }
    public int CategoryId { get; set; }
    
    // Tổng số lượng sách thêm vào kho
    public int StockQuantity { get; set; }
    
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public decimal? Price { get; set; }
}

public class UpdateBookDto
{
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public string? Publisher { get; set; }
    public DateTime? PublishedDate { get; set; }
    public int CategoryId { get; set; }
    
    // Số lượng cập nhật
    public int StockQuantity { get; set; }
    public int OnShelfQuantity { get; set; }
    
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public decimal? Price { get; set; }
    public int? BookshelfId { get; set; }
    public string? LocationCode { get; set; }
}

// DTO cho việc chuyển sách từ kho lên kệ
public class MoveToShelfDto
{
    public int BookId { get; set; }
    public int BookshelfId { get; set; }
    public string LocationCode { get; set; } = string.Empty;
    public int Quantity { get; set; }
}

// DTO cho sách trên kệ
public class BookOnShelf
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public int Quantity { get; set; }
    public int BorrowedQuantity { get; set; }
    public int AvailableQuantity => Quantity - BorrowedQuantity;
    public string BookshelfName { get; set; } = string.Empty;
    public string LocationCode { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
}